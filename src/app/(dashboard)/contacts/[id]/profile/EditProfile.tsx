import React from 'react';

import { Box, Flex, GridItem, SimpleGrid, Stack, Text } from '@chakra-ui/react';
import StringInput from '@/components/Input/StringInput';
import CommunicationGoals from '@/app/(dashboard)/consultations/view-all-consultations/communication-goals';
import { leadQualitiesOptions } from '@/data/options/consultations';
import { referralSourceOptions } from '@/data/options/consultations';
import CustomSelect from '@/components/Input/CustomSelect';
// import { provinceOptions } from '@/data/options/consultations';
import Email from './email';
//import TextEditor from '@/components/Input/CustomEditor';
import { Button } from '@/components/ui/button';
import { ContactStageOption } from '@/hooks/clients/useGetContactsStages';
import { useCountryStateCityHook } from '@/hooks/countryStateCity/useCountryStateCityHook';
import TextEditorNew from '@/components/Input/NewTextEditor';
import { Separator } from '@chakra-ui/react';

type EditP = {
  values: any;
  setFieldValue: any;
  handleFirstnameChange: any;
  handleLastnameChange: any;
  getClientHook: any;
  data: any;
  submitForm: any;
  contactStagesOptions: ContactStageOption[];
  handleDisplayNameChange: any;
  handleChange: any;
  setIsUserEditingDisplayName: any;
  updateLoading: any;
  setShowEditClient: any;
  error: any;
};

const EditProfile = ({
  values,
  setFieldValue,
  handleFirstnameChange,
  handleLastnameChange,
  handleDisplayNameChange,
  setShowEditClient,
  getClientHook,
  updateLoading,
  contactStagesOptions,
  data,
  setIsUserEditingDisplayName,
  submitForm,
  handleChange,
  error,
}: EditP) => {
  const { countryOptions, stateOptions, cityOptions } = useCountryStateCityHook(
    {
      countryCode: values?.country?.isoCode,
      stateCode: values?.state?.isoCode,
    }
  );
  return (
    <>
      <Stack gap={'2.5rem'} width={'100%'}>
        <SimpleGrid
          gap="2rem"
          columns={{ base: 1, md: 4 }}
          pb={'10'}
          borderBottom="1px solid"
          borderColor={'gray.50'}
        >
          <GridItem colSpan={1}>
            <Box>
              <Text
                fontSize={'lg'}
                fontWeight={'600'}
                textTransform={'capitalize'}
              >
                Client Information
              </Text>
              <Text mt={1} color={'gray.300'} fontSize={'md'} textWrap={'wrap'}>
                Client details autofill from scheduling and invoicing platforms.
              </Text>
            </Box>
          </GridItem>
          <GridItem colSpan={{ base: 1, md: 3 }}>
            <SimpleGrid gap={'2rem'} columns={{ base: 1, md: 3 }}>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'first_name',
                    value: values.first_name?.trim() || undefined,
                    onChange: handleFirstnameChange,
                  }}
                  fieldProps={{ label: 'First Name' }}
                />
              </Box>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'middle_name',
                    value: values.middle_name?.trim() || undefined,
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Middle Name' }}
                />
              </Box>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'last_name',
                    value: values.last_name?.trim() || undefined,
                    onChange: handleLastnameChange,
                  }}
                  fieldProps={{ label: 'Last Name' }}
                />
              </Box>
            </SimpleGrid>

            <SimpleGrid mt={'2rem'} gap={'2rem'} columns={1}>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'display_name',
                    value: values.display_name || '',
                    onChange: handleDisplayNameChange,
                    onBlur: (values: any) => {
                      if (values.display_name?.trim() === '') {
                        setIsUserEditingDisplayName('false'); // Allow reset only when empty
                      }
                    },
                  }}
                  fieldProps={{
                    label: 'Customer Display Name',
                    errorText: error,
                  }}
                  // error={error}
                />

                {error && (
                  <Text color="red" fontSize="sm" mt="3px">
                    First name and last name are required.
                  </Text>
                )}
              </Box>
            </SimpleGrid>

            {/* Personal Information Section */}
            <SimpleGrid mt={'2rem'} gap={'2rem'} columns={{ base: 1, md: 2 }}>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'phone',
                    value: values.phone?.trim() || undefined,
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Phone' }}
                />
              </Box>

              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'dob',
                    type: 'date',
                    value: values.dob ? values.dob.split('T')[0] : '',
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Date of Birth' }}
                />
              </Box>
            </SimpleGrid>

            <Box mt={'2rem'}>
              <Separator />
            </Box>
            {/* Address Information Section */}
            <SimpleGrid mt={'2rem'} gap={'2rem'} columns={{ base: 1, md: 2 }}>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'address',
                    value: values.address?.trim() || undefined,
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Address' }}
                />
              </Box>
              <Box>
                <StringInput
                  inputProps={{
                    borderColor: 'gray.100',
                    name: 'postal_code',
                    value: values.postal_code?.trim() || undefined,
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Postal Code' }}
                />
              </Box>
            </SimpleGrid>

            <SimpleGrid mt={'2rem'} gap={'2rem'} columns={{ base: 1, md: 3 }}>
              <Box>
                <CustomSelect
                  onChange={(option) => {
                    setFieldValue('country', option.value);
                  }}
                  selectedOption={countryOptions?.find(
                    (option) =>
                      option?.value?.isoCode == values?.country?.isoCode
                  )}
                  options={countryOptions}
                  label="Country"
                />
              </Box>

              <Box>
                {/* state */}
                <CustomSelect
                  onChange={(option) => {
                    setFieldValue('state', option.value);
                  }}
                  selectedOption={stateOptions?.find(
                    (option) => option?.value?.isoCode == values?.state?.isoCode
                  )}
                  options={stateOptions}
                  label="State/Province"
                />
              </Box>

              <Box>
                {/* city */}
                <CustomSelect
                  onChange={(option) => {
                    setFieldValue('city', option.value);
                  }}
                  selectedOption={cityOptions?.find(
                    (option) => option?.value?.name == values?.city?.name
                  )}
                  options={cityOptions}
                  label="City"
                />
              </Box>
            </SimpleGrid>
            <Box mt={'2rem'}>
              <Separator />
            </Box>

            <SimpleGrid gap={'2rem'} mt={'1.5rem'} columns={{ base: 1, md: 2 }}>
              <Box>
                <CustomSelect
                  controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                  placeholder="Select stage"
                  options={contactStagesOptions}
                  onChange={(val) => {
                    setFieldValue('stage', val.value);
                  }}
                  label="Stage"
                  defaultValue={contactStagesOptions.find(
                    (item) =>
                      item.value.toLowerCase() === values?.stage?.toLowerCase()
                  )}
                />
              </Box>
              <Box>
                <CustomSelect
                  controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                  placeholder="Active SLP"
                  options={getClientHook?.updateSlp}
                  onChange={(val) => {
                    setFieldValue('active_slp', val.value);
                  }}
                  label="Service Provider"
                  defaultValue={getClientHook?.updateSlp.find(
                    (item: any) => item.value === values?.active_slp?.id
                  )}
                />
              </Box>
            </SimpleGrid>
            <Box mt={'1.2rem'}>
              {/* <StringInput
                inputProps={{
                  value: data?.active_slp?.first_name
                    ? `${data?.active_slp?.first_name} ${data?.active_slp?.last_name}`
                    : '',
                }}
                fieldProps={{
                  label: 'Active SLP',
                  isReadOnly: true,
                }}
              /> */}
              {values?.stage?.toLowerCase() === 'sql' && (
                <Box>
                  <CustomSelect
                    controlStyle={{
                      border: '1px solid #a2a5ab',
                      borderRadius: '0.375rem',
                    }}
                    placeholder="Select lead quality"
                    options={leadQualitiesOptions}
                    onChange={(val) => {
                      setFieldValue('lead_quality', val.value);
                    }}
                    label="Quality"
                    defaultValue={leadQualitiesOptions.find(
                      (item) =>
                        item.value.toLowerCase() ===
                        values?.lead_quality?.toLowerCase()
                    )}
                  />
                </Box>
              )}
            </Box>
          </GridItem>
        </SimpleGrid>

        <SimpleGrid
          gap="2rem"
          pb={'10'}
          borderBottom="1px solid"
          borderColor={'gray.50'}
          columns={{ base: 1, md: 4 }}
        >
          <GridItem colSpan={1}>
            <Box>
              <Text
                fontSize={'lg'}
                fontWeight={'600'}
                textTransform={'capitalize'}
              >
                Admin Notes
              </Text>
              <Text mt={1} color={'gray.300'} fontSize={'md'} textWrap={'wrap'}>
                Notes to be filled by Receptionists
              </Text>
            </Box>
          </GridItem>
          <GridItem colSpan={{ base: 1, md: 3 }}>
            <SimpleGrid gap={'2rem'} columns={{ base: 1, md: 3 }}>
              <Box>
                <CustomSelect
                  controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                  placeholder="Select source"
                  options={referralSourceOptions}
                  onChange={(val) => {
                    setFieldValue('referral_source', val.value);
                  }}
                  label="Referral source"
                  defaultValue={
                    referralSourceOptions.find(
                      (item) =>
                        item.value.toLowerCase() ===
                        values?.referral_source?.toLowerCase()
                    ) || referralSourceOptions[0]
                  }
                />
              </Box>
              <Box height={'100%'} w={'100%'} zIndex={0}>
                <TextEditorNew
                  height="200px"
                  initialContent={values.notes}
                  saveContent={(e: any) => {
                    setFieldValue('notes', e);
                  }}
                />
              </Box>
              <Box>
                <CommunicationGoals
                  values={values as any}
                  setFieldValue={setFieldValue}
                />
              </Box>
            </SimpleGrid>
          </GridItem>
        </SimpleGrid>

        {/* ========================================================== */}
        {/* <SimpleGrid gap="2rem" columns={4}>
          <GridItem colSpan={1}>
            <Box>
              <Text fontWeight={600}>Client Information</Text>
              <Text mt={1} color={'gray.600'}>
                Client details autofill from scheduling and invoicing platforms.
              </Text>
            </Box>
          </GridItem>
          <GridItem colSpan={3}>
            <SimpleGrid gap={'2rem'} columns={3}>
              <Box>
                <CustomSelect
                controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                  placeholder="Select province"
                  options={provinceOptions}
                  onChange={(val) => {
                    setFieldValue('province', val.value);
                  }}
                  label="Province"
                  defaultValue={provinceOptions.find(
                    (item) =>
                      item.value.toLowerCase() ===
                      values?.province?.toLowerCase()
                  )}
                />
              </Box>
              <Box>
                <CustomSelect
                controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                  placeholder="Select status"
                  options={contactStagesOptions}
                  onChange={(val) => {
                    setFieldValue('stage', val.value);
                  }}
                  label="Status"
                  defaultValue={contactStagesOptions.find(
                    (item) =>
                      item.value.toLowerCase() === values?.stage?.toLowerCase()
                  )}
                />
              </Box>
              {values?.stage?.toLowerCase() === 'sql' && (
                <Box>
                  <CustomSelect
                  controlStyle={{
                    border: '1px solid #a2a5ab',
                    borderRadius: '0.375rem',
                  }}
                    placeholder="Select lead quality"
                    options={leadQualitiesOptions}
                    onChange={(val) => {
                      setFieldValue('lead_quality', val.value);
                    }}
                    label="Quality"
                    defaultValue={leadQualitiesOptions.find(
                      (item) =>
                        item.value.toLowerCase() ===
                        values?.lead_quality?.toLowerCase()
                    )}
                  />
                </Box>
              )}
            </SimpleGrid>
          </GridItem>
        </SimpleGrid> */}

        <Box alignSelf={'flex-start'}>
          <Text fontWeight={600}> Email Details</Text>
          <Email data={data} getClientHook={getClientHook} />
        </Box>

        <Box alignSelf={'flex-start'}>
          {/* something here */}
          {/* <Box my={'1rem'}>
            <Referrals data={data} />
          </Box> */}
          {/* <AddReferralModal data={data} getClientHook={getClientHook} /> */}
        </Box>

        <Box alignSelf={'flex-start'}>
          {/* <Text fontWeight={600}> Link User</Text>
            <Button mt={2} onClick={linkClientDisclosure.onOpen}>
              Link User
            </Button> */}
          {/* <Box mt={'1rem'}>
            <LinkedClients data={data} />
          </Box> */}
        </Box>

        <Flex
          gap={'2rem'}
          justifyContent={'flex-end'}
          position="fixed"
          bottom={'8'}
          right={'10'}
        >
          <Button onClick={() => setShowEditClient(false)}>Back</Button>
          <Button
            bg={'primary.500'}
            loading={updateLoading}
            onClick={submitForm}
          >
            Save
          </Button>
        </Flex>
      </Stack>
    </>
  );
};

export default EditProfile;
